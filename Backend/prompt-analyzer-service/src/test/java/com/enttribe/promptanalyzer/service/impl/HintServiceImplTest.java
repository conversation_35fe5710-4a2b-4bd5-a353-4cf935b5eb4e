package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.config.IntentHintConfig;
import com.enttribe.promptanalyzer.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;

import java.util.*;

@ExtendWith(MockitoExtension.class)
class HintServiceImplTest {

    @Mock
    private VectorStore vectorStore;

    @Mock
    private IntentHintConfig hintConfig;

    @InjectMocks
    private HintServiceImpl hintService;

    private Map<String, Object> requestMap;
    private Map<String, String> searchMap;
    private List<String> queries;
    private Document mockDocument;

    @BeforeEach
    void setUp() {
        // Setup request map for saveHint
        requestMap = new HashMap<>();
        requestMap.put("hintKeys", Arrays.asList("hint1", "hint2", "hint3"));
        requestMap.put("hintValue", "testHintValue");

        // Setup search map for searchPlan
        searchMap = new HashMap<>();
        searchMap.put("query", "test query");

        // Setup queries for batch operations
        queries = Arrays.asList("query1", "query2", "query3");

        // Setup mock document
        mockDocument = new Document("test content");
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("hintValue", "test result"); // Use the same key as hintConfig.getHintValue()
        metadata.put("filter", "Who");
        mockDocument.getMetadata().putAll(metadata);
    }

    @Test
    @DisplayName("Save hint successfully")
    void saveHintSuccess() {
        // Arrange
        when(hintConfig.getHintValue()).thenReturn("hintValue");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        doNothing().when(vectorStore).accept(anyList());

        // Act
        boolean result = hintService.saveHint(requestMap);

        // Assert
        assertTrue(result);
        verify(vectorStore, times(1)).accept(anyList());
        verify(hintConfig, times(1)).getHintValue();
        verify(hintConfig, times(3)).getHintFilter(); // Called once for each hint in the loop
    }

    @Test
    @DisplayName("Save hint failure - exception thrown")
    void saveHintFailure() {
        // Arrange
        when(hintConfig.getHintValue()).thenReturn("hintValue");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        doThrow(new RuntimeException("Vector store error")).when(vectorStore).accept(anyList());

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class,
            () -> hintService.saveHint(requestMap));

        assertEquals("unable to save hint", exception.getMessage());
        assertNotNull(exception.getCause());
        verify(vectorStore, times(1)).accept(anyList());
    }

    @Test
    @DisplayName("Search plan successfully")
    void searchPlanSuccess() {
        // Arrange
        when(hintConfig.getHintValue()).thenReturn("hintValue");
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");

        List<Document> documents = Arrays.asList(mockDocument);
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(documents);

        // Act
        String result = hintService.searchPlan(searchMap);

        // Assert
        assertEquals("test result", result);
        verify(vectorStore, times(1)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan - no hints found")
    void searchPlanNoHintsFound() {
        // Arrange
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);

        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(Collections.emptyList());

        // Act
        String result = hintService.searchPlan(searchMap);

        // Assert
        assertEquals("No hints found", result);
        verify(vectorStore, times(1)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan batch successfully")
    void searchPlanBatchSuccess() {
        // Arrange
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<Document> documents = Arrays.asList(mockDocument);
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(documents);

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatch(queries);

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size());

        for (int i = 0; i < results.size(); i++) {
            Map<String, Object> result = results.get(i);
            assertEquals("query" + (i + 1), result.get("name"));
            assertNotNull(result.get("metadata"));
            assertTrue(result.get("metadata") instanceof List);
        }

        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan batch - no results found")
    void searchPlanBatchNoResults() {
        // Arrange
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(Collections.emptyList());

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatch(queries);

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size());

        for (int i = 0; i < results.size(); i++) {
            Map<String, Object> result = results.get(i);
            assertEquals("query" + (i + 1), result.get("name"));
            List<?> metadata = (List<?>) result.get("metadata");
            assertTrue(metadata.isEmpty());
        }

        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan batch V1 with 'who' type")
    void searchPlanBatchV1WithWhoType() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<Document> documents = Arrays.asList(mockDocument);
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(documents);

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatchV1(queries, "who");

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size());
        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class));
        verify(hintConfig, times(1)).getHintFilter(); // Called once per batch, not per query
    }

    @Test
    @DisplayName("Search plan batch V1 with 'what' type")
    void searchPlanBatchV1WithWhatType() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<Document> documents = Arrays.asList(mockDocument);
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(documents);

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatchV1(queries, "what");

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size());
        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan batch V1 with partial name matching")
    void searchPlanBatchV1PartialNameMatching() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<String> fullNameQueries = Arrays.asList("John Doe", "Jane Smith");
        when(vectorStore.similaritySearch(any(SearchRequest.class)))
            .thenReturn(Collections.emptyList()) // First search returns empty
            .thenReturn(Arrays.asList(mockDocument)) // Second search (partial) returns result
            .thenReturn(Collections.emptyList()) // Third search returns empty
            .thenReturn(Arrays.asList(mockDocument)); // Fourth search (partial) returns result

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatchV1(fullNameQueries, "who");

        // Assert
        assertNotNull(results);
        assertEquals(2, results.size());
        verify(vectorStore, times(4)).similaritySearch(any(SearchRequest.class)); // 2 full + 2 partial searches
    }

    @Test
    @DisplayName("Search plan batch with entity type")
    void searchPlanBatchWithEntityType() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<Document> documents = Arrays.asList(mockDocument);
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(documents);

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatch(queries, "who", "person");

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size());
        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan batch with null entity type")
    void searchPlanBatchWithNullEntityType() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<Document> documents = Arrays.asList(mockDocument);
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(documents);

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatch(queries, "who", null);

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size());
        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class));
    }

    @Test
    @DisplayName("Search plan batch with entity type and partial matching")
    void searchPlanBatchWithEntityTypePartialMatching() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<String> fullNameQueries = Arrays.asList("John Doe");
        when(vectorStore.similaritySearch(any(SearchRequest.class)))
            .thenReturn(Collections.emptyList()) // First search returns empty
            .thenReturn(Arrays.asList(mockDocument)); // Second search (partial) returns result

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatch(fullNameQueries, "who", "person");

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        verify(vectorStore, times(2)).similaritySearch(any(SearchRequest.class)); // 1 full + 1 partial search
    }

    @Test
    @DisplayName("Save hint with empty hint keys")
    void saveHintWithEmptyHintKeys() {
        // Arrange
        Map<String, Object> emptyRequestMap = new HashMap<>();
        emptyRequestMap.put("hintKeys", Collections.emptyList());
        emptyRequestMap.put("hintValue", "testHintValue");

        when(hintConfig.getHintValue()).thenReturn("hintValue");
        doNothing().when(vectorStore).accept(anyList());

        // Act
        boolean result = hintService.saveHint(emptyRequestMap);

        // Assert
        assertTrue(result);
        verify(vectorStore, times(1)).accept(anyList());
    }

    @Test
    @DisplayName("Search plan batch V1 with empty query")
    void searchPlanBatchV1WithEmptyQuery() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<String> queriesWithEmpty = Arrays.asList("", "valid query");
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(Collections.emptyList());

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatchV1(queriesWithEmpty, "who");

        // Assert
        assertNotNull(results);
        assertEquals(2, results.size());
        // Empty query should not trigger partial search
        verify(vectorStore, times(3)).similaritySearch(any(SearchRequest.class)); // 2 full + 1 partial for "valid query"
    }

    @Test
    @DisplayName("Search plan batch V1 with single word query - no partial search needed")
    void searchPlanBatchV1SingleWordQuery() {
        // Arrange
        when(hintConfig.getExactMatchScoreThreshold()).thenReturn(0.95);
        when(hintConfig.getDefaultSimilarityThreshold()).thenReturn(0.8);
        when(hintConfig.getFilterExpressionFormat()).thenReturn("filter == '%s'");
        when(hintConfig.getHintFilter()).thenReturn("Who");
        when(hintConfig.getDefaultTopK()).thenReturn(5);
        when(hintConfig.getMetadataKey()).thenReturn("metadata");

        List<String> singleWordQueries = Arrays.asList("John");
        when(vectorStore.similaritySearch(any(SearchRequest.class))).thenReturn(Collections.emptyList());

        // Act
        List<Map<String, Object>> results = hintService.searchPlanBatchV1(singleWordQueries, "who");

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        // Single word query should trigger partial search with same word
        verify(vectorStore, times(2)).similaritySearch(any(SearchRequest.class)); // 1 full + 1 partial
    }
}
