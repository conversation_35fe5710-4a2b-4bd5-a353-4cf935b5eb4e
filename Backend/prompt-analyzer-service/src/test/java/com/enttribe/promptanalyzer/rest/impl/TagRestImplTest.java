package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.service.TagService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;

@WebMvcTest(TagRestImpl.class)
class TagRestImplTest {

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private TagService tagService;


//    @Test
//    @WithMockUser(username = "testUser", roles = {"API_TAG_CREATE"})
//    @DisplayName("Save tag success")
//    void saveTagSuccess() throws Exception {
//        Map<String, String> response = Map.of("result", "Tag saved successfully");
//
//        when(tagService.save(any(TagRequestDto.class))).thenReturn(response);
//
//        mockMvc.perform(post("/tag/create")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content("{}"))
//                .andExpect(status().isOk())
//                .andExpect(content().json("{\"result\":\"Tag saved successfully\"}"));
//    }



}
