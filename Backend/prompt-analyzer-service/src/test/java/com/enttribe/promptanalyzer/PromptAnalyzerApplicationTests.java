package com.enttribe.promptanalyzer;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

//@SpringBootTest
class PromptAnalyzerApplicationTests {

//	@Autowired
//	private ApplicationContext applicationContext;
//
//	@Test
//	void contextLoads() {
//		assertNotNull(applicationContext);
//	}
}
