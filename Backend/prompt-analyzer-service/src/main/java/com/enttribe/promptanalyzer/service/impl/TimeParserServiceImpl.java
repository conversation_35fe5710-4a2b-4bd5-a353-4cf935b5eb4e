package com.enttribe.promptanalyzer.service.impl;


import java.time.*;
import java.time.temporal.ChronoField;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.enttribe.promptanalyzer.config.RestTemplateSingleton;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.TimeParserService;
import edu.stanford.nlp.ling.CoreAnnotations;
import edu.stanford.nlp.ling.CoreLabel;
import edu.stanford.nlp.pipeline.Annotation;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TimeParserServiceImpl implements TimeParserService {

//    //@Value("${time.parser.url}")
//    private String timeParserUrl="http://localhost:8000/parse";
//
//    //@Value("${time.parser.timezone:UTC}")
//    private String timeParserTimeZone="Asia/Riyadh";

    @Value("${time.parser.url}")
    private String timeParserUrl;

    @Value("${time.parser.timezone:UTC}")
    private String timeParserTimeZone;

    private static final String VALUE_KEY = "value";
    private static final String TIME_DIMENSION = "time";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int WEEK_DAYS = 7;

    // Working week configuration
    private static final int WEEKEND_START_DAY = 5;   // Friday (DayOfWeek.FRIDAY.getValue())

    private final RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();

    private static final StanfordCoreNLP PIPELINE;
    private static final Pattern MONTH_REGEX =
            Pattern.compile("\\b(january|february|march|april|may|june|july|august|" +
                    "september|october|november|december)\\b", Pattern.CASE_INSENSITIVE);

    private static final String MONTHS =
            "(january|february|march|april|may|june|july|august|" +
                    "september|october|november|december)";

    private static final String QWORDS =
            "(first|1st|q1|second|2nd|q2|third|3rd|q3|fourth|4th|q4)\\s*(?:quarter|qtr)?";

    private static final LevenshteinDistance LV = LevenshteinDistance.getDefaultInstance();

    static {
        Properties p = new Properties();
        p.setProperty("annotators", "tokenize,ssplit,pos,lemma");
        PIPELINE = new StanfordCoreNLP(p);
    }

    private static final Map<String, String> TABLE = Map.ofEntries(
            Map.entry("jan", "january"), Map.entry("january", "january"),
            Map.entry("feb", "february"), Map.entry("february", "february"),
            Map.entry("mar", "march"), Map.entry("march", "march"),
            Map.entry("apr", "april"), Map.entry("april", "april"),
            Map.entry("may", "may"),
            Map.entry("jun", "june"), Map.entry("june", "june"),
            Map.entry("jul", "july"), Map.entry("july", "july"),
            Map.entry("aug", "august"), Map.entry("august", "august"),
            Map.entry("sep", "september"), Map.entry("sept", "september"),
            Map.entry("september", "september"),
            Map.entry("oct", "october"), Map.entry("october", "october"),
            Map.entry("nov", "november"), Map.entry("november", "november"),
            Map.entry("dec", "december"), Map.entry("december", "december")
    );
    /* month or quarter followed by optional 4-digit year */
    private static final Pattern P = Pattern.compile(
            "\\b(" + QWORDS + "|" + MONTHS + ")(?:\\s+(\\d{4}))?\\b",
            Pattern.CASE_INSENSITIVE);

    /**
     * quick scan for "after" or "before" (case-insensitive)
     */
    private static boolean hasAfterBefore(String q) {
        return q != null && q.toLowerCase().matches(".*\\b(after|before)\\b.*");
    }

    public static String augment(String query, ZoneId zone, Tense tense) {
        if (query.matches(".*\\b\\d{4}\\b.*")) {
            return query;
        }

        Matcher m = P.matcher(query);
        StringBuffer sb = new StringBuffer();
        LocalDate today = LocalDate.now(zone);
        int thisYear = today.getYear();

        while (m.find()) {
            String token = m.group(1);
            String yearStr = m.group(2);

            // if user already typed a year → leave token unchanged
            if (yearStr != null) {
                m.appendReplacement(sb, m.group());
                continue;
            }

            /* -------- Quarter handling -------- */
            int quarter = quarterNumber(token);
            if (quarter != 0) {
                LocalDate startQ = LocalDate.of(thisYear, 1 + (quarter - 1) * 3, 1);
                LocalDate endQ = startQ.plusMonths(3);      // exclusive

                switch (tense) {
                    case FUTURE, PRESENT -> startQ = endQ.isBefore(today) ? startQ.plusYears(1) : startQ;
                    case PAST -> startQ = startQ.isAfter(today) ? startQ.minusYears(1) : startQ;
                }
                m.appendReplacement(sb, token + " " + startQ.getYear());
                continue;
            }

            /* -------- Month handling (typo-tolerant) -------- */
            String canon = canonicalMonth(token);
            if (canon != null) {
                int monthNum = Month.valueOf(canon.toUpperCase(Locale.ROOT)).getValue();
                int year = switch (tense) {
                    case FUTURE -> (monthNum < today.getMonthValue()) ? thisYear + 1 : thisYear;
                    case PRESENT -> thisYear;
                    case PAST -> (monthNum > today.getMonthValue()) ? thisYear - 1 : thisYear;
                };
                m.appendReplacement(sb, canon + " " + year);
                continue;
            }

            // fallback – not month/quarter
            m.appendReplacement(sb, m.group());
        }
        m.appendTail(sb);
        return sb.toString();
    }

    /**
     * 1-4, or 0 if token is not a quarter word.
     */
    private static int quarterNumber(String token) {
        token = token.toLowerCase(Locale.ROOT).replaceAll("\\s+", "");
        return switch (token) {
            case "first", "1st", "q1" -> 1;
            case "second", "2nd", "q2" -> 2;
            case "third", "3rd", "q3" -> 3;
            case "fourth", "4th", "q4" -> 4;
            default -> 0;
        };
    }

    /**
     * returns canonical month or null
     */
    private static String canonicalMonth(String token) {
        String[][] map = {
                {"jan", "january"}, {"feb", "february"}, {"mar", "march"},
                {"apr", "april"}, {"may", "may"}, {"jun", "june"}, {"jul", "july"},
                {"aug", "august"}, {"sep", "september"}, {"sept", "september"},
                {"oct", "october"}, {"nov", "november"}, {"dec", "december"}
        };
        String low = token.toLowerCase(Locale.ROOT);
        for (String[] row : map) {
            String abbr = row[0], full = row[1];
            int dist = LV.apply(low, full);
            if (dist == 0 || (full.length() >= 5 && dist <= 2) || (full.startsWith(low) && low.length() >= 3))
                return full;
            if (low.equals(abbr)) return full;
        }
        return null;
    }

    /**
     * Null if token is not (even fuzzy) a month.
     */
    public static String canonical(String token) {
        return TABLE.get(token.toLowerCase());
    }

    /**
     * int 1-12 or -1 if not a month.
     */
    public static int monthNumber(String token) {
        String c = canonical(token);
        return c == null ? -1 : Month.valueOf(c.toUpperCase()).getValue();
    }

    public enum Tense {PAST, PRESENT, FUTURE}


    /**
     * Main entry: pass the raw user query, get a query with years filled in.
     */
    private static final Pattern MONTH_WITH_DAY_PATTERN =
            Pattern.compile("\\b(january|february|march|april|may|june|july|august|september|october|november|december)\\s+\\d{1,2}(st|nd|rd|th)?\\b", Pattern.CASE_INSENSITIVE);

    private static final Pattern MONTH_ONLY_PATTERN =
            Pattern.compile("\\b(january|february|march|april|may|june|july|august|september|october|november|december)\\b", Pattern.CASE_INSENSITIVE);


    public String addYearIfMissing(String query, ZoneId zone) {
        // Skip augmentation if a 4-digit year is already present
        if (query.matches(".*\\b(19|20)\\d{2}\\b.*")) {
            return query;
        }

        // Skip if full month with day (e.g., March 5th) is present
//        if (MONTH_WITH_DAY_PATTERN.matcher(query).find()) {
//            return query;
//        }

        Matcher m = MONTH_ONLY_PATTERN.matcher(query);
        if (m.find()) {
            String month = m.group(1);
            int currentYear = Year.now(zone).getValue();
            Tense tense = detectTense(query);
            System.out.println("Tense detected "+tense.toString());
            int resolvedYear = currentYear; // default
            Month inputMonth = Month.valueOf(month.toUpperCase());

            switch (tense) {
                case PAST -> {
                    if (inputMonth.getValue() > ZonedDateTime.now(zone).getMonthValue()) {
                        resolvedYear = currentYear - 1;
                    }
                }
                case FUTURE -> {
                    if (inputMonth.getValue() < ZonedDateTime.now(zone).getMonthValue()) {
                        resolvedYear = currentYear + 1;
                    }
                }
                case PRESENT -> resolvedYear = currentYear;
            }

            return m.replaceFirst(month + " " + resolvedYear);
        }

        return query;
    }



    /* ----------- tense detector (very simple heuristic) ------------- */
    private static Tense detectTense(String text) {
        Annotation doc = new Annotation(text);
        PIPELINE.annotate(doc);

        boolean hasPast = false, hasFuture = false;
        List<CoreLabel> toks = doc.get(CoreAnnotations.TokensAnnotation.class);

        for (int i = 0; i < toks.size(); i++) {
            CoreLabel tok = toks.get(i);
            String pos = tok.get(CoreAnnotations.PartOfSpeechAnnotation.class); // VBD, MD, ...
            String lemma = tok.get(CoreAnnotations.LemmaAnnotation.class).toLowerCase();

            /* 1 ─ Past tense or past perfect */
            if (pos.equals("VBD") || pos.equals("VBN")) {
                hasPast = true;
            }

            /* 2 ─ Canonical future modal  ("will", "shall") */
            if (pos.equals("MD") && ("will".equals(lemma) || "shall".equals(lemma))) {
                hasFuture = true;
                continue;
            }

            /* 3 ─ “be going to <VB>” future */
            if ("going".equals(lemma) && i >= 1 && i + 1 < toks.size()) {
                String prevLemma = toks.get(i - 1).lemma().toLowerCase();
                String nextPos = toks.get(i + 1).tag();                 // should be "TO"
                if (("be".equals(prevLemma) || "am".equals(prevLemma) ||
                        "is".equals(prevLemma) || "are".equals(prevLemma)) &&
                        "TO".equals(nextPos)) {
                    hasFuture = true;
                    continue;
                }
            }

            /* 4 ─ Intent verbs  (“plan / planning / intend”) +  "to <VB>" */
            if ((lemma.equals("plan") || lemma.equals("intend")) &&
                    i + 1 < toks.size() &&
                    "TO".equals(toks.get(i + 1).tag())) {
                hasFuture = true;
            }
        }

        /* Decide tense */
        Tense tense;
        if (hasFuture && !hasPast) tense = Tense.FUTURE;
        else if (hasPast && !hasFuture) tense = Tense.PAST;
        else tense = Tense.PRESENT;
        return tense;
    }



    private Optional<ZonedDateTime> extractEndFromTimeNode(JsonNode timeRoot, ZoneId zoneId) {
        for (JsonNode node : timeRoot) {
            JsonNode val = node.path("value");
            if (val.has("to")) {
                String endStr = val.path("to").path("value").asText(null);
                if (endStr != null) return Optional.of(ZonedDateTime.parse(endStr).withZoneSameInstant(zoneId));
            }
        }
        return Optional.empty();
    }


    @Override
    public String parseTimeFromQuery(Map<String, String> request) {
        String userQuery = request.get("userQuery");
        String timeZone = request.getOrDefault("timeZone", timeParserTimeZone);
        String locale = request.getOrDefault("locale", "en_US");
        ZoneId userZone = ZoneId.of(timeZone);

        log.info("Parsing time from query: {} timeZone: {} locale: {}", userQuery, timeZone, locale);

        try {
            // STEP 1: Try Duration-based detection first
            String durJson = callDucklingDims(userQuery, timeZone, locale, "[\"duration\"]");
            log.info("Received duration: {}", durJson);

            JsonNode durRoot = objectMapper.readTree(durJson);
            if (durRoot.isArray() && !durRoot.isEmpty()) {
                JsonNode durVal = durRoot.get(0).path("value");
                int amount = durVal.path("value").asInt();
                String unit = durVal.path("unit").asText();
                Tense tense = detectTense(userQuery);
                log.info("Tense detected is {}", tense);

                ZonedDateTime ref = ZonedDateTime.of(2025, 6, 1, 0, 0, 0, 0, userZone); // FIXED SYSTEM DATE for testing
                ZonedDateTime start, end;

                switch (tense) {
                    case FUTURE, PRESENT -> {
                        start = ref;
                        end = switch (unit) {
                            case "day" -> ref.plusDays(amount);
                            case "week" -> ref.plusWeeks(amount);
                            case "month" -> ref.plusMonths(amount);
                            case "year" -> ref.plusYears(amount);
                            default -> ref.plusSeconds(durVal.path("normalized").path("value").asLong());
                        };
                    }
                    case PAST -> {
                        end = ref;
                        start = switch (unit) {
                            case "day" -> ref.minusDays(amount);
                            case "week" -> ref.minusWeeks(amount);
                            case "month" -> ref.minusMonths(amount);
                            case "year" -> ref.minusYears(amount);
                            default -> ref.minusSeconds(durVal.path("normalized").path("value").asLong());
                        };
                    }
                    default -> throw new IllegalStateException("Unexpected value: " + tense);
                }

                // Handle Saudi-specific week
                if ("Asia/Riyadh".equals(timeZone) && "week".equals(unit)) {
                    start = start.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
                    end = start.plusDays((long) amount * 7);
                }

                return formatDateRange(start, end);
            }

            // STEP 2: Try Time-based parsing
            String preprocessed = addYearIfMissing(userQuery, userZone);
            log.debug("Query after month-year augmentation: {}", preprocessed);

            String timeJson = callDucklingDims(preprocessed, timeZone, locale, "[\"time\"]");
            log.debug("Received time: {}", timeJson);

            JsonNode timeRoot = objectMapper.readTree(timeJson);
            if (timeRoot.isArray() && !timeRoot.isEmpty()) {
                ZonedDateTime extractedStart = extractStartFromTimeNode(timeRoot, userZone).orElse(null);
                ZonedDateTime extractedEnd = extractEndFromTimeNode(timeRoot, userZone).orElse(null);

                if (extractedStart != null) {
                    if ("Asia/Riyadh".equals(timeZone)) {
                        extractedStart = extractedStart.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
                        if (extractedEnd != null) {
                            extractedEnd = extractedStart.plusDays(7);
                        }
                    }
                    return formatDateRange(extractedStart, extractedEnd);
                }
            }

            // STEP 3: Fallback logic if no duration or time was successful
            return processTimeResponse(timeJson, userQuery.toLowerCase());

        } catch (Exception e) {
            log.error("Error parsing time from query: {}", e.getMessage(), e);
            throw new BusinessException("Failed to parse time from query");
        }
    }



//    @Override
//    public String parseTimeFromQuery(Map<String, String> request) {
//        String userQuery = request.get("userQuery");
//        String timeZone = request.getOrDefault("timeZone", timeParserTimeZone);
//        String locale = request.getOrDefault("locale", "en_US");
//        ZoneId userZone = ZoneId.of(timeZone);
//
//        log.info("Parsing time from query: {} timeZone: {} locale: {}",
//                userQuery, timeZone, locale);
//
//        try {
//            String durJson = callDucklingDims(userQuery, timeZone, locale, "[\"duration\"]");
//            System.out.println("Received duration: {}"+ durJson);
//
//            JsonNode durRoot = objectMapper.readTree(durJson);
//            if (durRoot.isArray() && !durRoot.isEmpty()) {
//                JsonNode durVal = durRoot.get(0).path("value");
//                int amount = durVal.path("value").asInt();
//                String unit = durVal.path("unit").asText();
//                Tense tense = detectTense(userQuery);
//                System.out.println("Tense detected is {}"+ tense);
//
//                // Try extracting explicit start date (e.g., "from 25 March")
//                String timeJson = callDucklingDims(userQuery, timeZone, locale, "[\"time\"]");
//                JsonNode timeRoot = objectMapper.readTree(timeJson);
//                System.out.println("Received time: {}"+ timeJson);
//
//                Optional<ZonedDateTime> startOpt = Optional.empty();
//                if (timeRoot.isArray()) {
//                    for (JsonNode node : timeRoot) {
//                        JsonNode val = node.path("value");
//                        if (val.has("from")) {
//                            String fromStr = val.path("from").path("value").asText();
//                            startOpt = Optional.of(ZonedDateTime.parse(fromStr));
//                            break;
//                        } else if (val.has("value")) {
//                            String fromStr = val.path("value").asText();
//                            startOpt = Optional.of(ZonedDateTime.parse(fromStr));
//                            break;
//                        }
//                    }
//                }
//
//                ZonedDateTime start, end;
//
//                if (startOpt.isPresent()) {
//                    start = startOpt.get().withHour(0).withMinute(0).withSecond(0).withNano(0);
//                    end = switch (unit) {
//                        case "day" -> start.plusDays(amount);
//                        case "week" -> start.plusWeeks(amount);
//                        case "month" -> start.plusMonths(amount);
//                        case "year" -> start.plusYears(amount);
//                        default -> start.plusSeconds(durVal.path("normalized").path("value").asLong());
//                    };
//                } else {
//                    ZonedDateTime ref = ZonedDateTime.now(userZone)
//                            .withHour(0).withMinute(0).withSecond(0).withNano(0);
//                    switch (tense) {
//                        case FUTURE, PRESENT -> {
//                            start = ref;
//                            end = switch (unit) {
//                                case "day" -> ref.plusDays(amount);
//                                case "week" -> ref.plusWeeks(amount);
//                                case "month" -> ref.plusMonths(amount);
//                                case "year" -> ref.plusYears(amount);
//                                default -> ref.plusSeconds(durVal.path("normalized").path("value").asLong());
//                            };
//                        }
//                        case PAST -> {
//                            end = ref;
//                            start = switch (unit) {
//                                case "day" -> ref.minusDays(amount);
//                                case "week" -> ref.minusWeeks(amount);
//                                case "month" -> ref.minusMonths(amount);
//                                case "year" -> ref.minusYears(amount);
//                                default -> ref.minusSeconds(durVal.path("normalized").path("value").asLong());
//                            };
//                        }
//                        default -> throw new IllegalStateException("Unexpected value: " + tense);
//                    }
//                }
//
//                // Special handling for week in Saudi
//                if ("Asia/Riyadh".equalsIgnoreCase(timeZone) && "week".equals(unit)) {
//                    DayOfWeek saudiStart = DayOfWeek.SUNDAY;
//                    start = start.with(TemporalAdjusters.previousOrSame(saudiStart));
//                    end = start.plusWeeks(tense == Tense.PAST ? 1 : amount);
//                }
//
//                return formatDateRange(start, end);
//            }
//
//            // No duration found, fallback to time dimension
//            String preprocessed = addYearIfMissing(userQuery, userZone);
//            System.out.println("Query after month-year augmentation: {}"+ preprocessed);
//
//            String responseBody = callDucklingDims(preprocessed, timeZone, locale, "[\"time\"]");
//            System.out.println("Received time: {}"+ responseBody);
//
//            JsonNode timeRoot = objectMapper.readTree(responseBody);
//            ZonedDateTime extractedStart = extractStartFromTimeNode(timeRoot, userZone).orElse(null);
//            ZonedDateTime extractedEnd = extractEndFromTimeNode(timeRoot, userZone).orElse(null);
//
//            if (extractedStart != null) {
//                // Saudi week adjustment
//                if ("Asia/Riyadh".equals(timeZone)) {
//                    DayOfWeek startDay = extractedStart.getDayOfWeek();
//                    if (startDay != DayOfWeek.SUNDAY) {
//                        extractedStart = extractedStart.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
//                    }
//
//                    if (extractedEnd != null) {
//                        extractedEnd = extractedStart.plusDays(7); // adjust to next week
//                    }
//                }
//
//                return formatDateRange(extractedStart, extractedEnd);
//            }
//
//
//            return processTimeResponse(responseBody, userQuery.toLowerCase());
//
//        } catch (Exception e) {
//            log.error("Error parsing time from query: {}", e.getMessage(), e);
//            throw new BusinessException("Failed to parse time from query");
//        }
//    }


    private String resolveClosestDayFromDuckling(JsonNode valuesArray, ZoneId zone, Tense tense) {
        if (valuesArray == null || !valuesArray.isArray() || valuesArray.size() == 0) return null;

        ZonedDateTime now = ZonedDateTime.now(zone);
        ZonedDateTime bestMatch = null;

        for (JsonNode val : valuesArray) {
            String dateStr = val.path("value").asText();
            if (dateStr == null || dateStr.isEmpty()) continue;

            ZonedDateTime candidate = ZonedDateTime.parse(dateStr);
            switch (tense) {
                case PAST -> {
                    if (candidate.isBefore(now) &&
                            (bestMatch == null || candidate.isAfter(bestMatch))) {
                        bestMatch = candidate;
                    }
                }
                case FUTURE -> {
                    if (candidate.isAfter(now) &&
                            (bestMatch == null || candidate.isBefore(bestMatch))) {
                        bestMatch = candidate;
                    }
                }
                case PRESENT -> {
                    if (bestMatch == null ||
                            Math.abs(candidate.toEpochSecond() - now.toEpochSecond()) <
                                    Math.abs(bestMatch.toEpochSecond() - now.toEpochSecond())) {
                        bestMatch = candidate;
                    }
                }
            }
        }

        return bestMatch != null ? formatDateRange(bestMatch, bestMatch.plusDays(1)) : null;
    }


    @Override
    public String addYearIfMissing(Map<String, String> request) {
        String userQuery = request.get(PromptConstants.USER_QUERY);
        log.debug("userQuery before: {}", userQuery);
        String addedYearIfMissing = addYearIfMissing(userQuery, null);
        log.debug("userQuery after adding year : {}", addedYearIfMissing);
        return callDucklingDims(addedYearIfMissing, timeParserTimeZone, "en_US", "[\"time\"]");
    }



    /**
     * Processes the response from the time parser API.
     *
     * @param responseBody The JSON response from the API
     * @return The formatted time information
     * @throws JsonProcessingException If there's an error processing the JSON
     */
    /**
     * Processes the response from Duckling and chooses the
     * “strongest” time entity instead of always using index 0.
     */
    private String processTimeResponse(String responseBody,
                                       String originalQuery) throws JsonProcessingException {

        log.debug("Duckling response, {}", responseBody);
        JsonNode rootNode = objectMapper.readTree(responseBody);

        if (!rootNode.isArray() || rootNode.isEmpty()) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        /* ---------- pick best candidate ---------- */
        JsonNode bestNode = null;
        int bestRank = Integer.MAX_VALUE;  // lower = better
        int bestSpan = -1;                 // char length of body

        for (JsonNode node : rootNode) {
            String grain = node.path("value").path("grain").asText("");
            int rank = switch (grain) {
                case "quarter" -> 1;
                case "month" -> 2;
                case "week" -> 3;
                case "day" -> 4;
                case "hour" -> 5;
                case "minute" -> 6;
                case "second" -> 7;
                default -> 8;
            };
            int span = node.path("end").asInt() - node.path("start").asInt();

            if (rank < bestRank || (rank == bestRank && span > bestSpan)) {
                bestRank = rank;
                bestSpan = span;
                bestNode = node;
            }
        }

        if (bestNode == null) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        /* ---------- process the chosen node ---------- */
        String body = bestNode.path("body").asText("").toLowerCase();
        JsonNode value = bestNode.path(VALUE_KEY);

        return processExtractedTimeValue(value, body, originalQuery);
    }


    private String processExtractedTimeValue(JsonNode valueNode,
                                             String body,
                                             String originalQuery) {
        log.debug("Processing extracted time value: {} with body: {}", valueNode, body);
        if (valueNode == null) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        String type = valueNode.get("type").asText();
        boolean isWeekendQuery = body.contains("weekend") || originalQuery.contains("weekend");

        return switch (type) {
            case VALUE_KEY -> processValueType(valueNode, isWeekendQuery, originalQuery);
            case "interval" -> processIntervalType(valueNode, isWeekendQuery, originalQuery); // ★ changed
            case "array" -> processArrayType(valueNode, isWeekendQuery);
            default -> {
                log.info("Unsupported time type: {}", type);
                yield "Unsupported time type: " + type;
            }
        };
    }

    /**
     * Handles Duckling "type":"value" tokens.
     * <p>
     * • MONTH grain:
     * 1. Keep the existing typo-fix & year anchoring (when multiple future years).
     * 2. Then delegate to processDateByGrain(...) so the final output is
     * "from: <1st-of-month> to: <1st-of-next-month>".
     * <p>
     * • Other grains: behaviour unchanged.
     */
    private String processValueType(JsonNode valueNode,
                                    boolean isWeekendQuery,
                                    String originalQuery) {

        String grain = valueNode.has("grain")
                ? valueNode.get("grain").asText()
                : "day";

        JsonNode valuesNode = valueNode.get("values");

        /* ── MONTH grain ─────────────────────────────────────────────── */
        if ("month".equalsIgnoreCase(grain)) {

            String iso;
            // 1) first candidate
            if (valuesNode != null && valuesNode.size() > 0) {
                iso = valuesNode.get(0).get(VALUE_KEY).asText();
            } else {
                iso = valueNode.get(VALUE_KEY).asText();
            }

            // 2) anchor to current year when user didn't specify a year
            boolean userSuppliedYear = originalQuery.matches(".*\\d{4}.*");
            if (!userSuppliedYear && valuesNode != null && valuesNode.size() > 1) {
                ZonedDateTime dt = parseDateTime(iso);
                dt = dt.withYear(ZonedDateTime.now(dt.getZone()).getYear());
                iso = dt.toString();
            }

            // **3) NEW – if query has 'after' or 'before', keep single value**
            String qLow = originalQuery.toLowerCase(Locale.ROOT);
            if (qLow.contains("after") || qLow.contains("before")) {
                return iso;                // e.g. "2024-01-01T00:00:00+03:00"
            }

            // 4) otherwise build the whole-month range (existing behaviour)
            return processDateByGrain(iso, grain, isWeekendQuery, originalQuery);
        }

        /* ── Weekend hour override (unchanged) ───────────────────────── */
        if (isWeekendQuery && "hour".equalsIgnoreCase(grain) &&
                valuesNode != null && valuesNode.size() > 0) {

            String dateStr = valuesNode.get(0).get(VALUE_KEY).asText();
            return processDateByGrain(dateStr, grain, true, originalQuery);
        }
        /* ── NEW: handle ambiguous 'day' grain values like "March 5th" ── */
        Optional<ZonedDateTime> bestMatch = extractStartFromTimeNode(valueNode, ZoneId.of("Asia/Riyadh"));
        if (bestMatch.isPresent()) {
            return processDateByGrain(bestMatch.get().toOffsetDateTime().toString(), grain, isWeekendQuery, originalQuery);
        }


        /* ── All other grains keep existing behaviour ────────────────── */
        if (valuesNode != null && valuesNode.size() > 0) {
            StringBuilder sb = new StringBuilder();
            for (JsonNode v : valuesNode) {
                if (sb.length() > 0) sb.append(" OR ");
                sb.append(processDateByGrain(
                        v.get(VALUE_KEY).asText(), grain, isWeekendQuery, originalQuery));
            }
            return sb.toString();
        }

        if (valueNode.has(VALUE_KEY)) {
            return processDateByGrain(
                    valueNode.get(VALUE_KEY).asText(), grain, isWeekendQuery, originalQuery);
        }

        return "PromptConstants.NO_TIME_INFORMATION_FOUND";
    }

    /**
     * Handles Duckling intervals, including cases where Duckling supplies
     * only one bound (e.g. “from last week”, “until next month”).
     * <p>
     * Rules for fabricating the missing bound
     * ---------------------------------------
     * grain == day    → ±1 day
     * grain == week   → Saudi business week based on the supplied date
     * grain == month  → ±1 calendar month (JDK handles 28/29/30/31)
     * grain == year   → ±1 calendar year (365 / 366 handled by java.time)
     * <p>
     * Weekend queries (“this weekend”) are still mapped to the Fri–Sat range.
     */
    private String processIntervalType(JsonNode valueNode,
                                       boolean isWeekendQuery,
                                       String originalQuery) {

        JsonNode fromNode = valueNode.get("from");
        JsonNode toNode = valueNode.get("to");

        /* ── 0-A  SPECIAL:  after / before + MONTH  ─────────────────── */
        String q = originalQuery.toLowerCase(Locale.ROOT);

        // "after <month>"  → Duckling gives only a FROM bound
        if (q.contains("after") &&
                fromNode != null && toNode == null &&
                "month".equalsIgnoreCase(fromNode.path("grain").asText(""))) {
            return fromNode.get(VALUE_KEY).asText();      // e.g. 2024-01-01T00…
        }

        // "before <month>" → Duckling gives only a TO bound
        if (q.contains("before") &&
                toNode != null && fromNode == null &&
                "month".equalsIgnoreCase(toNode.path("grain").asText(""))) {
            return toNode.get(VALUE_KEY).asText();        // e.g. 2024-06-01T00…
        }

        /* 0 ─ Weekend shortcut ─────────────────────────────────────────── */
        if (isWeekendQuery && fromNode != null) {
            ZonedDateTime fromDt = parseDateTime(fromNode.get(VALUE_KEY).asText());
            return processWeekend(fromDt);                     // Fri–Sat
        }

        if (fromNode != null && toNode != null) {

            String fromGrain = fromNode.path("grain").asText("");
            String toGrain = toNode.path("grain").asText("");
            String toValue = toNode.get(VALUE_KEY).asText();

            // Duckling pattern: from = now (grain second)  to = day-start 00:00
            ZonedDateTime toDt = parseDateTime(toValue);
            boolean isDayStart = toDt.getHour() == 0
                    && toDt.getMinute() == 0
                    && toDt.getSecond() == 0
                    && toDt.getNano() == 0;

            if ("second".equals(fromGrain) && "second".equals(toGrain) && isDayStart) {
                /*  ⇨   Return only the target day (single ISO)   */
                return toValue;                   // << your app gets "2025-05-27T00:00+03:00"
                // Or, if you really need a one-day range:
                // return String.format("from: %s to: %s", toValue, toDt.plusDays(1));
            }
        }

        /* 1 ─ Both bounds present → simple clean range ─────────────────── */
        if (fromNode != null && toNode != null) {
            return String.format("from: %s to: %s",
                    fromNode.get(VALUE_KEY).asText(),
                    toNode.get(VALUE_KEY).asText());
        }

        /* helper: parse ISO to ZonedDateTime */
        Function<JsonNode, ZonedDateTime> toDate =
                n -> parseDateTime(n.get(VALUE_KEY).asText());

        /* 2 ─ Only FROM present → fabricate TO ─────────────────────────── */
        if (fromNode != null) {
            String grain = fromNode.has("grain") ? fromNode.get("grain").asText() : "";
            ZonedDateTime start = toDate.apply(fromNode);

            return switch (grain.toLowerCase()) {
                case "day" -> formatDateRange(start, start.plusDays(1));
                case "week" -> processWorkWeek(ZonedDateTime.now(start.getZone()), "last week");
                case "month" -> formatDateRange(start, start.plusMonths(1));
                case "quarter" -> formatDateRange(start, start.plusMonths(3));
                case "year" -> formatDateRange(start, start.plusYears(1));
                default -> String.format("from: %s", start.toString());
            };
        }

        /* 3 ─ Only TO present → fabricate FROM (rare) ─────────────────── */
        if (toNode != null) {
            String grain = toNode.has("grain") ? toNode.get("grain").asText() : "";
            ZonedDateTime end = toDate.apply(toNode);

            return switch (grain.toLowerCase()) {
                case "day" -> formatDateRange(end.minusDays(1), end);
                case "week" -> processWorkWeek(ZonedDateTime.now(end.getZone()), "last week");
                case "month" -> formatDateRange(end.minusMonths(1), end);
                case "year" -> formatDateRange(end.minusYears(1), end);
                default -> String.format("to: %s", end.toString());
            };
        }

        return "PromptConstants.NO_TIME_INFORMATION_FOUND";
    }

    private String processArrayType(JsonNode arrayNode, boolean isWeekendQuery) {
        if (!arrayNode.isArray()) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        StringBuilder result = new StringBuilder();
        for (JsonNode item : arrayNode) {
            if (result.length() > 0) {
                result.append(" AND ");
            }
            result.append(processExtractedTimeValue(item, "", ""));
        }
        return result.toString();
    }


    /**
     * Converts one ISO timestamp from Duckling into the desired output.
     * • WEEK   → Saudi business-week (Sun-Thu) range
     * • MONTH  → single ISO date (YYYY-MM-01T00:00…) – no range, no year shifting
     * • Other grains keep their existing range behaviour.
     */
    private String processDateByGrain(String dateValue,
                                      String grain,
                                      boolean isWeekendQuery,
                                      String originalQuery) {
        log.debug("Processing date by grain: {}  grain={} weekend={}",
                dateValue, grain, isWeekendQuery);

        try {
            ZonedDateTime dt = parseDateTime(dateValue);

            /* Weekend queries override everything */
            if (isWeekendQuery) {
                return processWeekend(dt);
            }

            switch (grain.toLowerCase()) {

                /* ── SECOND / MINUTE / HOUR ─────────────────────────────── */
                case "minute", "hour" -> {
                    ZonedDateTime end = dt.plusHours(1);
                    return formatDateRange(dt, end);
                }

                /* ── DAY ───────────────────────────────────────────────── */
                case "day" -> {
//                    boolean wholeDay = dt.getHour() == 0 && dt.getMinute() == 0;
//                    ZonedDateTime end = wholeDay ? dt.plusDays(1)
//                            : dt.plusHours(1);
//                    return formatDateRange(dt, end);
                    return dt.toString();
                }

                /* ── WEEK  (Saudi business week) ─────────────────────── */
                case "week" -> {
                    return processWorkWeek(dt, originalQuery);
                }

                /* ── MONTH  (single ISO, no year correction) ─────────── */
                case PromptConstants.MONTH -> {
                    /* 1. normalise to first day 00:00 of the month Duckling returned */
                    ZonedDateTime start = dt.withDayOfMonth(1)
                            .withHour(0).withMinute(0)
                            .withSecond(0).withNano(0);

                    /* 2. exclusive upper-bound = first day of the next month */
                    ZonedDateTime end = start.plusMonths(1);

                    return formatDateRange(start, end);
                }
                /* ── QUARTER ─────────────────────────────────────────── */
                case "quarter" -> {
                    // normalise the start to the first day 00:00
                    ZonedDateTime start = dt.withDayOfMonth(1)
                            .withHour(0).withMinute(0)
                            .withSecond(0).withNano(0);

                    // exclusive upper bound: start + 3 months
                    ZonedDateTime end = start.plusMonths(3);

                    return formatDateRange(start, end);
                }


                /* ── YEAR ────────────────────────────────────────────── */
                case "year" -> {
                    ZonedDateTime start = dt.with(TemporalAdjusters.firstDayOfYear());
                    ZonedDateTime end = dt.with(TemporalAdjusters.lastDayOfYear())
                            .plusDays(1);
                    return formatDateRange(start, end);
                }

                case "decade" -> {
                    ZonedDateTime end = dt.plusYears(10);
                    return formatDateRange(dt, end);
                }
                case "century" -> {
                    ZonedDateTime end = dt.plusYears(100);
                    return formatDateRange(dt, end);
                }
                case "millennium" -> {
                    ZonedDateTime end = dt.plusYears(1000);
                    return formatDateRange(dt, end);
                }
                /* ── fallback: leave untouched ───────────────────────── */
                //default -> dateValue;
            }

        } catch (Exception e) {
            log.warn("Error processing date by grain: {}", e.getMessage());
            return dateValue;
        }
        return dateValue;
    }

    /**
     * Builds a range for a Saudi working week (Sun 00:00 → Fri 00:00).
     *
     * <p>Duckling always gives you the ISO-8601 Monday of the week that the
     * expression refers to.  That works fine for “next week” and “last week”,
     * but when <b>today is Sunday</b> (Saudi start-of-week) Duckling’s Monday is
     * <i>six days ago</i>.  To get the right Sunday we:</p>
     *
     * <ol>
     *   <li>Detect whether the query is “this”, “next”, or “last” week by looking
     *       at <code>originalQuery</code>.</li>
     *   <li>Choose a <i>reference date</i> based on that:
     *       <ul>
     *         <li>this/current week → “now”</li>
     *         <li>next week         → now&nbsp;+ 7 days</li>
     *         <li>last/previous     → now&nbsp;− 7 days</li>
     *       </ul></li>
     *   <li>Snap that reference date backwards (or keep it) to the
     *       <b>nearest Sunday 00:00</b>.</li>
     * </ol>
     *
     * @param mondayStart   The ISO Monday provided by Duckling
     * @param originalQuery The user’s raw query (lower/upper case doesn’t matter)
     * @return "from: <Sun 00:00> to: <Fri 00:00>"
     */

    private String processWorkWeek(ZonedDateTime mondayStart,
                                   String originalQuery) {

        ZoneId zone = mondayStart.getZone();
        ZonedDateTime today = ZonedDateTime.now(zone);

        String q = originalQuery == null ? "" : originalQuery.toLowerCase();

        /* ---------- choose reference date --------------------------- */
        ZonedDateTime ref;
        if (q.contains("next") && !q.contains("last")) {
            ref = today.plusWeeks(1);
        } else if (q.contains("last") || q.contains("previous")) {
            ref = today.minusWeeks(1);
        } else {
            // no relative keyword → use mondayStart *unless* it is the same ISO week as today
            int thisIsoWeek = today.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            int startIsoWeek = mondayStart.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);

            ref = (thisIsoWeek == startIsoWeek && today.getYear() == mondayStart.getYear())
                    ? today              // "this week"
                    : mondayStart;       // absolute week supplied by Duckling
        }

        /* ---------- snap to Sunday 00:00 and build Saudi range ------ */
        ZonedDateTime weekStart = ref
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY)) // ← FIX
                .withHour(0).withMinute(0)
                .withSecond(0).withNano(0);

        // exclusive upper bound: Friday 00:00 (Sun + 5 days)
        ZonedDateTime weekEnd = weekStart.plusDays(5);

        return formatDateRange(weekStart, weekEnd);
    }


    /**
     * Process weekend (Friday and Saturday)
     */
    /**
     * Builds a Saudi weekend range:
     * Friday 00:00 (inclusive)
     * →     Sunday 00:00 (exclusive)
     * so the range covers **all of Friday and Saturday only**.
     *
     * @param dateTime any timestamp that Duckling gave inside the target
     *                 weekend (could be Fri, Sat, or an hour on either day)
     * @return "from: <ISO> to: <ISO>" string
     */
    private String processWeekend(ZonedDateTime dateTime) {

        /* 1 ─ Walk back (if needed) until we land on Friday */
        ZonedDateTime weekendStart = dateTime;
        while (weekendStart.getDayOfWeek().getValue() != WEEKEND_START_DAY) { // 5 == Friday
            weekendStart = weekendStart.minusDays(1);
        }

        /* 2 ─ Normalise to Friday 00:00 */
        weekendStart = weekendStart.withHour(0).withMinute(0)
                .withSecond(0).withNano(0);

        /* 3 ─ Exclusive upper bound: Sunday 00:00 (Friday + 2 days) */
        ZonedDateTime weekendEnd = weekendStart.plusDays(2);

        /* 4 ─ Return formatted range */
        return formatDateRange(weekendStart, weekendEnd);
    }

    /**
     * Parses a date-time string from the API into a ZonedDateTime object.
     *
     * @param dateTimeStr The date-time string to parse
     * @return Parsed ZonedDateTime object
     */
    private ZonedDateTime parseDateTime(String dateTimeStr) {
        // The API returns dates in ISO-8601 format
        return ZonedDateTime.parse(dateTimeStr);
    }

    /*
     * Formats a date range as a string.
     *
     * @param start The start date-time
     * @param end The end date-time
     * @return Formatted date range string
     */

    /**
     * Formats a date range as a string, always using ISO-8601 with
     * numeric offset only (no “…[Asia/Riyadh]” suffix).
     */
    private String formatDateRange(ZonedDateTime start, ZonedDateTime end) {
        return String.format("from: %s to: %s",
                start.toOffsetDateTime().toString(),
                end.toOffsetDateTime().toString());
    }


    /**
     * Generic Duckling call for an arbitrary dims list.
     */
    private String callDucklingDims(String query,
                                    String tz,
                                    String locale,
                                    String dimsJson) {

        HttpHeaders h = new HttpHeaders();
        h.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        String body = String.format("locale=%s&tz=%s&text=%s&dims=%s",
                locale, tz, query, dimsJson);

        return restTemplate.postForObject(timeParserUrl,
                new HttpEntity<>(body, h),
                String.class);
    }


    private Optional<ZonedDateTime> extractStartFromTimeNode(JsonNode timeNode, ZoneId zoneId) {
        JsonNode values = timeNode.path("values");
        if (values.isArray() && values.size() > 0) {
            // Prefer date from current year
            int currentYear = Year.now(zoneId).getValue();
            for (JsonNode val : values) {
                String valueStr = val.path("value").asText(null);
                if (valueStr != null) {
                    ZonedDateTime zdt = ZonedDateTime.parse(valueStr).withZoneSameInstant(zoneId);
                    if (zdt.getYear() == currentYear) {
                        return Optional.of(zdt);
                    }
                }
            }
            // If no value matched current year, return first one
            String firstValue = values.get(0).path("value").asText(null);
            if (firstValue != null) {
                return Optional.of(ZonedDateTime.parse(firstValue).withZoneSameInstant(zoneId));
            }
        }

        return Optional.empty();
    }


    public static void main(String[] args) {
        TimeParserServiceImpl timeParserService = new TimeParserServiceImpl();

        // Test cases
        Map<String, String> request = new HashMap<>();
        request.put("timeZone", "Asia/Riyadh"); // Set Saudi timezone
        request.put("locale", "en_US");

        // Test Case 1: When today is Sunday
        request.put("userQuery", "show me list of employees who joined in last 2 weeks");
        // request.put("userQuery", "show me all documets shared wth me from last week");
        // request.put("userQuery", "show me 5 employees of it department");
        // request.put("userQuery", "between april to june 2026");
        runTest(timeParserService, request);


    }

    private static void runTest(TimeParserServiceImpl service, Map<String, String> request) {
        try {
            System.out.println("Input Query: " + request.get("userQuery"));
            System.out.println("TimeZone: " + request.get("timeZone"));
            System.out.println("Locale: " + request.get("locale"));

            String result = service.parseTimeFromQuery(request);

            System.out.println("\nFinal Output:");
            System.out.println(result);
            System.out.println("=====================================");
        } catch (Exception e) {
            log.error("error in running tests : {}", e.getMessage(), e);
        }
    }
}