package com.enttribe.promptanalyzer.service;


import java.util.List;
import java.util.Map;

public interface HintService {

    boolean saveHint(Map<String, Object> requestMap);

    String searchPlan(Map<String, String> map);

    List<Map<String, Object>> searchPlanBatch(List<String> queries);

    List<Map<String, Object>> searchPlanBatchV1(List<String> queries, String type);

    List<Map<String, Object>> searchPlanBatch(List<String> queries, String type, String entityType);

}
