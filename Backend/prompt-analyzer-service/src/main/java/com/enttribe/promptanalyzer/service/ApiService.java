package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;

import java.util.List;
import java.util.Map;

/**
 * Service interface for managing API operations related to web crawling.
 * This interface defines methods for triggering crawls, checking task status, 
 * retrieving table schemas, and getting table names.
 * Author: VisionWaves
 * Version: 1.0
 */
public interface ApiService {

    String triggerCrawl(String websiteUrl);
    CrawlResponse getTaskStatus(String taskId);
    String getTableSchema(String executionName, String tableName);
    List<String> getTablesName(String name);
    Map<String, String> getProcessors();

}
