package com.enttribe.promptanalyzer.util;

import ai.djl.huggingface.tokenizers.HuggingFaceTokenizer;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.FileAttribute;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Set;

@Component
public class ChunkCounter {

    @Value("${tokenizer.path}")
    private Resource tokenizerResource;

    private HuggingFaceTokenizer tokenizer;

    @PostConstruct
    public void init() throws IOException {
        // Use secure temp file with restricted permissions
        Set<PosixFilePermission> perms = PosixFilePermissions.fromString("rw-------");
        FileAttribute<Set<PosixFilePermission>> attr = PosixFilePermissions.asFileAttribute(perms);
        Path tempPath = Files.createTempFile("tokenizer", ".json", attr);

        Files.copy(tokenizerResource.getInputStream(), tempPath, StandardCopyOption.REPLACE_EXISTING);
        tokenizer = HuggingFaceTokenizer.newInstance(tempPath);
    }

    public int countChunks(String text) {
        return tokenizer.encode(text).getTokens().length;
    }
}
