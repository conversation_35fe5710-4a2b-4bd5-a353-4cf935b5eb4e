package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.rest.HintRest;
import com.enttribe.promptanalyzer.service.HintService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/hint")
@RequiredArgsConstructor
public class HintRestImpl implements HintRest {

    private final HintService hintService;

    @Override
    public Map<String, Boolean> saveHints(Map<String, Object> requestMap) {
        boolean saved = hintService.saveHint(requestMap);
        return Map.of("result", saved);
    }

    @Override
    public Map<String, String> searchPlan(Map<String, String> map) {
        String plan = hintService.searchPlan(map);
        return Map.of("result", plan);
    }

    @Override
    public List<Map<String, Object>> searchPlanBatch(List<String> queries) {
        log.info("Processing batch search request with {} queries", queries.size());
        return hintService.searchPlanBatch(queries);
    }

    @Override
    public List<Map<String, Object>> searchPlanBatchV1(List<String> queries, String type) {
        log.info("Processing batch search request with {} queries and type {}", queries.size(), type);
        return hintService.searchPlanBatchV1(queries, type);
    }

    @Override
    public List<Map<String, Object>> searchPlanBatchV2(Map<String, Object> request) {
        List<String> queries = (List<String>) request.get("queries");
        String type = (String) request.get("type");
        String entityType = (String) request.get("entity");
        log.info("Processing batch search request with {} queries type : {} entityType : {}",
                queries.size(), type, entityType);
        return hintService.searchPlanBatch(queries, type, entityType);
    }

}
