package com.enttribe.promptanalyzer.dto.prompt;

import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Tool;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;

import java.util.List;
import java.util.Set;

/**
 * A Data Transfer Object (DTO) that represents a complete prompt configuration
 * with validation constraints. Used for creating and updating prompt configurations
 * with parameter validation.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class PromptDto {

    private Integer id;
    private String application;
    private String category;
    private String promptId;
    private String status;

    @DecimalMin(value = "0.0", message = "Temperature must be greater than or equal to 0.0")
    @DecimalMax(value = "2.0", message = "Temperature must be less than or equal to 2.0")
    private Double temperature;

    @Min(value = 1, message = "Max tokens must be at least 1 and cannot be negative")
    private Integer maxTokens;

    private String version;

    @DecimalMin(value = "0.0", message = "topP must be greater than or equal to 0.0")
    @DecimalMax(value = "1.0", message = "topP must be less than or equal to 1.0")
    private Double topP;

    private String name;
    private String type;
    private String assertionTemplate;
    private String defaultFormat;
    private List<Message> messages;
    private String model;
    private String inference;
    private String provider;
    private Set<Tool> tools;
    private Boolean jsonMode;
    private Boolean llmGuard;
    private String tags;
}

