package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/*
 * REST client interface for hint management operations.
 * Provides CRUD operations for managing hints through Feign client.
 * Author: VisionWaves
 * Version: 1.0
 */
@FeignClient(name = "AgentRest", url = "${prompt-analyzer-service.url}", path = "/hint", primary = false)
public interface HintRest {

    /**
     * Saves hints based on the provided request map.
     * Requires ROLE_API_HINT_SAVE security role.
     *
     * @param requestMap The request map containing hint data
     * @return Map containing the result of the save operation
     */
    @Operation(
            summary = "Save hints",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_CREATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints saved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/save")
    Map<String, Boolean> saveHints(@RequestBody Map<String, Object> requestMap);

    /**
     * Searches for hints based on the provided map.
     * Requires ROLE_API_HINT_SEARCH security role.
     *
     * @param map The search parameters map
     * @return Map containing search results
     */
    @Operation(
            summary = "Search for hints",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_SEARCH})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints search successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/search")
    Map<String, String> searchPlan(@RequestBody Map<String, String> map);

    /**
     * Searches for hints in batch mode.
     * Requires ROLE_API_HINT_SEARCH_BATCH security role.
     *
     * @param queries List of query strings to search for
     * @return List of maps containing search results
     */
    @Operation(
            summary = "Search for hints in batch",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_SEARCH_BATCH})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints search successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/search-batch")
    List<Map<String, Object>> searchPlanBatch(@RequestBody List<String> queries);

    /**
     * Searches for hints in batch with type specification.
     * Requires ROLE_API_HINT_SEARCH_BATCH security role.
     *
     * @param queries List of query strings to search for
     * @param type The type of search to perform
     * @return List of maps containing search results
     */
    @Operation(
            summary = "Search for hints in batch with type",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_SEARCH_BATCH_V1})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints search successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/search-batch/{type}")
    List<Map<String, Object>> searchPlanBatchV1(@RequestBody List<String> queries, @PathVariable String type);

    /**
     * Searches for hints in batch with type specification.
     * Requires ROLE_API_HINT_SEARCH_BATCH security role.
     *
     * @param request List of query strings to search for
     * @return List of maps containing search results
     */
    @Operation(
            summary = "Search for hints in batch with type",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_SEARCH_BATCH_V1})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints search successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/v1/search-batch")
    List<Map<String, Object>> searchPlanBatchV2(@RequestBody Map<String, Object> request);

}
