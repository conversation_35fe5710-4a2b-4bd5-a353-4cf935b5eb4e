package com.enttribe.promptanalyzer.constants;

/**
 * Constants used throughout the API for authentication, authorization, and responses.
 * Contains role definitions, status codes, and access scopes for different API endpoints.
 * Author: VisionWaves
 * Version: 1.0
 */
public class APIConstants {


    private APIConstants() {
    }

    public static final String RESULT = "result";
    public static final String SUCCESS = "success";
    public static final String FAILED = "failed";
    public static final String SUCCESS_CODE = "200";
    public static final String DEFAULT_SCHEME = "default";
    public static final String ERROR = "Internal server error";
    public static final String ERROR_CODE = "500";

    // Tool scopes - Action-based
    public static final String ROLE_API_TOOL_CREATE = "ROLE_API_TOOL_CREATE";
    public static final String ROLE_API_TOOL_UPDATE = "ROLE_API_TOOL_UPDATE";
    public static final String ROLE_API_TOOL_DELETE = "ROLE_API_TOOL_DELETE";
    public static final String ROLE_API_TOOL_SEARCH = "ROLE_API_TOOL_SEARCH";
    public static final String ROLE_API_TOOL_COUNT = "ROLE_API_TOOL_COUNT";
    public static final String ROLE_API_TOOL_GET_BY_ID = "ROLE_API_TOOL_GET_BY_ID";
    public static final String ROLE_API_TOOL_GET_BY_IDS = "ROLE_API_TOOL_GET_BY_IDS";
    public static final String ROLE_API_TOOL_EXISTS = "ROLE_API_TOOL_EXISTS";
    public static final String ROLE_API_TOOL_EXPORT = "ROLE_API_TOOL_EXPORT";
    public static final String ROLE_API_TOOL_IMPORT = "ROLE_API_TOOL_IMPORT";
    public static final String ROLE_API_TOOL_CHECK_COMPILATION = "ROLE_API_TOOL_CHECK_COMPILATION";
    public static final String ROLE_API_TOOL_GET_BY_APPLICATION = "ROLE_API_TOOL_GET_BY_APPLICATION";
    public static final String ROLE_API_TOOL_GET_BY_AGENT_ID = "ROLE_API_TOOL_GET_BY_AGENT_ID";
    public static final String ROLE_API_TOOL_CHANGE_STATUS = "ROLE_API_TOOL_CHANGE_STATUS";
    public static final String ROLE_API_TOOL_GET_BY_NAME = "ROLE_API_TOOL_GET_BY_NAME";
    public static final String ROLE_API_TOOL_GET_BY_IDS_V1 = "ROLE_API_TOOL_GET_BY_IDS_V1" ;
    public static final String ROLE_API_TOOL_REGISTER_AS_AGENT = "ROLE_API_TOOL_REGISTER_AS_AGENT";

    // LlmModel scopes - Action-based
    public static final String ROLE_API_LLMMODEL_CREATE = "ROLE_API_LLMMODEL_CREATE";
    public static final String ROLE_API_LLMMODEL_GET_BY_PROVIDER = "ROLE_API_LLMMODEL_GET_BY_PROVIDER";
    public static final String ROLE_API_LLMMODEL_GET_ALL = "ROLE_API_LLMMODEL_GET_ALL";
    public static final String ROLE_API_LLMMODEL_GET_FOR_SDK = "ROLE_API_LLMMODEL_GET_FOR_SDK";
    public static final String ROLE_API_LLMMODEL_GET_BY_TYPE = "ROLE_API_LLMMODEL_GET_BY_TYPE";

    // Test case scopes
    public static final String ROLE_API_TESTCASE_GENERATE = "ROLE_API_TESTCASE_GENERATE";
    public static final String ROLE_API_TESTCASE_CREATE = "ROLE_API_TESTCASE_CREATE";
    public static final String ROLE_API_TESTCASE_CREATE_BATCH = "ROLE_API_TESTCASE_CREATE_BATCH" ;
    public static final String ROLE_API_TESTCASE_UPDATE = "ROLE_API_TESTCASE_UPDATE";
    public static final String ROLE_API_TESTCASE_DELETE = "ROLE_API_TESTCASE_DELETE";
    public static final String ROLE_API_TESTCASE_SEARCH = "ROLE_API_TESTCASE_SEARCH";
    public static final String ROLE_API_TESTCASE_COUNT = "ROLE_API_TESTCASE_COUNT";
    public static final String ROLE_API_TESTCASE_EXPORT = "ROLE_API_TESTCASE_EXPORT";
    public static final String ROLE_API_TESTCASE_IMPORT = "ROLE_API_TESTCASE_IMPORT";


    // Audit API scopes - Action-based
    public static final String ROLE_API_AUDIT_EXCEPTION_SAVE = "ROLE_API_AUDIT_EXCEPTION_SAVE";
    public static final String ROLE_API_AUDIT_PROMPT_SAVE = "ROLE_API_AUDIT_PROMPT_SAVE";
    public static final String ROLE_API_AUDIT_TOOL_SAVE = "ROLE_API_AUDIT_TOOL_SAVE";
    public static final String ROLE_API_AUDIT_PROMPT_GET_BY_AUDIT_ID = "ROLE_API_AUDIT_PROMPT_GET_BY_AUDIT_ID";
    public static final String ROLE_API_AUDIT_PROMPT_GET_BY_PROMPT_ID = "ROLE_API_AUDIT_PROMPT_GET_BY_PROMPT_ID";
    public static final String ROLE_API_AUDIT_PROMPT_SEARCH = "ROLE_API_AUDIT_PROMPT_SEARCH";
    public static final String ROLE_API_AUDIT_PROMPT_COUNT = "ROLE_API_AUDIT_PROMPT_COUNT";
    public static final String ROLE_API_AUDIT_TOOL_GET_BY_AUDIT_ID = "ROLE_API_AUDIT_TOOL_GET_BY_AUDIT_ID";

    // Llm API scopes - Action-based
    public static final String ROLE_API_CHAT_COMPLETE = "ROLE_API_CHAT_COMPLETE";
    public static final String ROLE_API_EXECUTE_PROMPT = "ROLE_API_EXECUTE_PROMPT";
    public static final String ROLE_API_EXECUTE_PROMPT_V1 = "ROLE_API_EXECUTE_PROMPT_V1";
    public static final String ROLE_API_GENERATE_SYSTEM_PROMPT = "ROLE_API_GENERATE_SYSTEM_PROMPT";

    // trigger API scopes
    public static final String ROLE_API_TRIGGER_GET_BY_NAME = "ROLE_API_TRIGGER_GET_BY_NAME";
    public static final String ROLE_API_TRIGGER_UPDATE = "ROLE_API_TRIGGER_UPDATE";
    public static final String ROLE_API_TRIGGER_CREATE = "ROLE_API_TRIGGER_CREATE";
    public static final String ROLE_API_TRIGGER_COUNT = "ROLE_API_TRIGGER_COUNT";
    public static final String ROLE_API_TRIGGER_SEARCH = "ROLE_API_TRIGGER_SEARCH";

    // Agent API scopes
    public static final String ROLE_API_AGENT_CREATE = "ROLE_API_AGENT_CREATE";
    public static final String ROLE_API_AGENT_SEARCH = "ROLE_API_AGENT_SEARCH";
    public static final String ROLE_API_AGENT_GET_HISTORY = "ROLE_API_AGENT_GET_HISTORY";
    public static final String ROLE_API_AGENT_PLAN_CREATE = "ROLE_API_AGENT_PLAN_CREATE";
    public static final String ROLE_API_AGENT_TRIGGER_CREATE = "ROLE_API_AGENT_TRIGGER_CREATE";
    public static final String ROLE_API_AGENT_TRIGGER_NAME_CREATE = "ROLE_API_AGENT_TRIGGER_NAME_CREATE";

    // Processor API scopes - Action-based
    public static final String ROLE_API_PROCESSOR_CREATE = "ROLE_API_PROCESSOR_CREATE";
    public static final String ROLE_API_PROCESSOR_UPDATE = "ROLE_API_PROCESSOR_UPDATE";
    public static final String ROLE_API_PROCESSOR_SEARCH = "ROLE_API_PROCESSOR_SEARCH";
    public static final String ROLE_API_PROCESSOR_COUNT = "ROLE_API_PROCESSOR_COUNT";

    // Query API scopes - Action-based
    public static final String ROLE_API_QUERY_CREATE = "ROLE_API_QUERY_CREATE";
    public static final String ROLE_API_QUERY_DELETE = "ROLE_API_QUERY_DELETE";
    public static final String ROLE_API_QUERY_SEARCH = "ROLE_API_QUERY_SEARCH";
    public static final String ROLE_API_QUERY_COUNT = "ROLE_API_QUERY_COUNT";

    // Tag API scopes - Action-based
    public static final String ROLE_API_TAG_CREATE = "ROLE_API_TAG_CREATE";
    public static final String ROLE_API_TAG_UPDATE = "ROLE_API_TAG_UPDATE";
    public static final String ROLE_API_TAG_DELETE = "ROLE_API_TAG_DELETE";
    public static final String ROLE_API_TAG_SEARCH = "ROLE_API_TAG_SEARCH";
    public static final String ROLE_API_TAG_COUNT = "ROLE_API_TAG_COUNT";

    // Knowledge Base scopes - Action-based
    public static final String ROLE_API_KNOWLEDGE_BASE_CREATE = "ROLE_API_KNOWLEDGE_BASE_CREATE";
    public static final String ROLE_API_KNOWLEDGE_BASE_UPDATE = "ROLE_API_KNOWLEDGE_BASE_UPDATE";
    public static final String ROLE_API_KNOWLEDGE_BASE_DELETE = "ROLE_API_KNOWLEDGE_BASE_DELETE";
    public static final String ROLE_API_KNOWLEDGE_BASE_COUNT = "ROLE_API_KNOWLEDGE_BASE_COUNT";
    public static final String ROLE_API_KNOWLEDGE_BASE_SEARCH = "ROLE_API_KNOWLEDGE_BASE_SEARCH";
    public static final String ROLE_API_KNOWLEDGE_BASE_GET_BY_ID = "ROLE_API_KNOWLEDGE_BASE_GET_BY_ID";
    public static final String ROLE_API_KNOWLEDGE_BASE_GET_BY_IDS = "ROLE_API_KNOWLEDGE_BASE_GET_BY_IDS";
    public static final String ROLE_API_KNOWLEDGE_BASE_EXISTS = "ROLE_API_KNOWLEDGE_BASE_EXISTS";
    public static final String ROLE_API_KNOWLEDGE_BASE_SHOW_TABLES = "ROLE_API_KNOWLEDGE_BASE_SHOW_TABLES";
    public static final String ROLE_API_KNOWLEDGE_BASE_MILVUS_IMPORT_DATA = "ROLE_API_KNOWLEDGE_BASE_MILVUS_IMPORT_DATA";
    public static final String ROLE_API_KNOWLEDGE_BASE_EXPORT = "ROLE_API_KNOWLEDGE_BASE_EXPORT";
    public static final String ROLE_API_KNOWLEDGE_BASE_IMPORT = "ROLE_API_KNOWLEDGE_BASE_IMPORT";
    public static final String ROLE_API_KNOWLEDGE_BASE_EXISTS_BY_NAME = "ROLE_API_KNOWLEDGE_BASE_EXISTS_BY_NAME";

    // MCP Server scopes - Action-based
    public static final String ROLE_API_MCP_SERVER_CREATE = "ROLE_API_MCP_SERVER_CREATE";
    public static final String ROLE_API_MCP_SERVER_UPDATE = "ROLE_API_MCP_SERVER_UPDATE";
    public static final String ROLE_API_MCP_SERVER_DELETE = "ROLE_API_MCP_SERVER_DELETE";
    public static final String ROLE_API_MCP_SERVER_SEARCH = "ROLE_API_MCP_SERVER_SEARCH";
    public static final String ROLE_API_MCP_SERVER_COUNT = "ROLE_API_MCP_SERVER_COUNT";
    public static final String ROLE_API_MCP_SERVER_GET_BY_IDS = "ROLE_API_MCP_SERVER_GET_BY_IDS";

    // Hint API scopes - Action-based
    public static final String ROLE_API_HINT_SEARCH = "ROLE_API_HINT_SEARCH";
    public static final String ROLE_API_HINT_SEARCH_BATCH = "ROLE_API_HINT_SEARCH_BATCH";
    public static final String ROLE_API_HINT_SEARCH_BATCH_V1 = "ROLE_API_HINT_SEARCH_BATCH_V1";
    public static final String ROLE_API_HINT_CREATE = "ROLE_API_HINT_CREATE";

    // Time Parser scopes - Action-based
    public static final String ROLE_API_TIME_PARSER_PARSE = "ROLE_API_TIME_PARSER_PARSE";
    public static final String ROLE_API_TIME_PARSER_UPDATE = "ROLE_API_TIME_PARSER_UPDATE";

    // Tool Callback Provider scopes
    public static final String ROLE_API_TOOL_CALLBACK_GET_LIST = "ROLE_API_TOOL_CALLBACK_GET_LIST";

    // Prompt API scopes - Action-based
    public static final String ROLE_API_PROMPT_CREATE = "ROLE_API_PROMPT_CREATE";
    public static final String ROLE_API_PROMPT_UPDATE = "ROLE_API_PROMPT_UPDATE";
    public static final String ROLE_API_PROMPT_DELETE = "ROLE_API_PROMPT_DELETE";
    public static final String ROLE_API_PROMPT_SEARCH = "ROLE_API_PROMPT_SEARCH";
    public static final String ROLE_API_PROMPT_COUNT = "ROLE_API_PROMPT_COUNT";
    public static final String ROLE_API_PROMPT_GET_BY_ID = "ROLE_API_PROMPT_GET_BY_ID";
    public static final String ROLE_API_PROMPT_GET_BY_NAME = "ROLE_API_PROMPT_GET_BY_NAME";
    public static final String ROLE_API_PROMPT_GET_BY_APP_NAME = "ROLE_API_PROMPT_GET_BY_APP_NAME";
    public static final String ROLE_API_PROMPT_EXPORT = "ROLE_API_PROMPT_EXPORT";
    public static final String ROLE_API_PROMPT_IMPORT = "ROLE_API_PROMPT_IMPORT";
    public static final String ROLE_API_PROMPT_FILTER = "ROLE_API_PROMPT_FILTER";
    public static final String ROLE_API_PROMPT_EXPORT_BY_IDS = "ROLE_API_PROMPT_EXPORT_BY_IDS";
    public static final String ROLE_API_PROMPT_SEARCH_EXISTS = "ROLE_API_PROMPT_SEARCH_EXISTS";
    public static final String ROLE_API_PROMPT_GET_DIS_APP_NAME = "ROLE_API_PROMPT_GET_DIS_APP_NAME";
    public static final String ROLE_API_PROMPT_GET_DIS_APP_AND_CATE = "ROLE_API_PROMPT_GET_DIS_APP_AND_CATE";
    public static final String ROLE_API_PROMPT_UPDATE_ASSERTION_TEMP = "ROLE_API_PROMPT_UPDATE_ASSERTION_TEMP";
    public static final String ROLE_API_PROMPT_SEARCH_V1 = "ROLE_API_PROMPT_SEARCH_V1";
    public static final String ROLE_API_PROMPT_UPDATE_TAG = "ROLE_API_PROMPT_UPDATE_TAG";

}

